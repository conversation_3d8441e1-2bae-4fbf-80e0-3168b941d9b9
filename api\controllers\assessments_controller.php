<?php
class AssessmentsController extends AppController {

	var $name = 'Assessments';
	var $uses = array('Assessment','AssessmentRequest','StudentUpdate');


	function beforeFilter(){
		parent::beforeFilter();
		// Add authentication rules specific to assessments if needed
		if($this->isAPIRequest()){
			// Allow POST requests to add, preview_email, send_email, print_form, and preview_attachment
			$this->Auth->allow(array('index'));
		}
	}

	function index() {
		$this->Assessment->recursive = 0; // Keep recursion low for index view
		// Add containable behavior if you need associated data efficiently
		$this->paginate['Assessment']['contain'] = array('Student', 'Section','AssessmentRequest.id','AssessmentRequest.ref_no','AssessmentRequest.student_name'); // Example
		$assessments = $this->paginate();
		if(count($assessments)==1):
			App::import('Model','MasterConfig');
			App::import('Model','Api.Jwt');
			$CONF = new MasterConfig();
			$JWT = new JWT();
			$secretKey = $CONF->findBySysKey('AGIMAT_SECRET')['MasterConfig']['sys_value'];
			$AID = $assessments[0]['Assessment']['id'];
			$assessments[0]['Assessment']['token']=$JWT->generateJWT($AID, $secretKey);
			// Use assessment request CJR as student_id 
			if(empty($assessments[0]['Student'])):
				if(!empty($assessments[0]['AssessmentRequest'])):
					$assessments[0]['Assessment']['student_id'] =$assessments[0]['AssessmentRequest']['ref_no'];
				endif;
			endif;
		endif;
		$this->set(compact('assessments'));
	}

	function view($id = null) {

		if (!$id) {
			// Use API error handling if it's an API request
			if ($this->isAPIRequest()) {
				return $this->cakeError('error400', array('message' => 'Invalid Assessment ID'));
			}
			$this->Session->setFlash(__('Invalid assessment ID', true));
			$this->redirect(array('action' => 'index'));
		}
		// Use containable to fetch associated data
		$this->Assessment->contain(array(
			'Student',
			'Section',
			'AssessmentFee', // Example: Include Fee details with AssessmentFee
			'AssessmentPaysched'
		));
		if($id=='preview_email')
			return $this->preview_email();

		if($id=='preview_attachment')
			return $this->preview_attachment();

		if($id=='print_form')
			return $this->print_form();

		$assessment = $this->Assessment->findById($id);
		if (!$assessment) {
			if ($this->isAPIRequest()) {
				return $this->cakeError('error404', array('message' => 'Assessment not found'));
			}
			$this->Session->setFlash(__('Assessment not found', true));
			$this->redirect(array('action' => 'index'));
		}
		$this->set('assessment', $assessment);
	}

	function add() {
		// Expecting data in $this->data['Assessment'] matching the example structure
		if (!empty($this->data) && isset($this->data['Assessment'])) {
			$incomingData = $this->data['Assessment'];
			$summaryDetails = isset($incomingData['summary_details']) ? $incomingData['summary_details'] : array();
			$AID = $this->Assessment->generateAID('CJA25');

			// 1. Prepare Assessment data
			$assessmentData = array();
			$assessmentData['id'] = $AID; // Generate Assessment ID
			$assessmentData['student_id'] = isset($incomingData['student_id']) ? $incomingData['student_id'] : null;
			$assessmentData['section_id'] = null; // Per instruction
			$assessmentData['account_type'] = 'student'; // Per instruction
			$assessmentData['esp'] = 2025.00; // Per instruction, ensure correct decimal format if needed
			$assessmentData['ref_no'] = isset($incomingData['request_no']) ? $incomingData['request_no'] : null; // Per instruction
			$assessmentData['account_details'] = json_encode($summaryDetails); // Store summary as JSON
			$assessmentData['payment_scheme'] = isset($incomingData['payment_plan_id']) ? $incomingData['payment_plan_id']:'CASH' ;
			$assessmentData['assessment_total'] = isset($incomingData['assessment_total']) ? $incomingData['assessment_total'] : 0.00;

			// Determine subsidy status
			$assessmentData['subsidy_status'] = null; // Default
			if (isset($summaryDetails['discount_details']) && is_array($summaryDetails['discount_details'])) {
				foreach ($summaryDetails['discount_details'] as $discount) {
					// Basic check, might need refinement based on actual discount types
					if (stripos($discount['type'], 'ESC') !== false) {
						$assessmentData['subsidy_status'] = 'ESC';
						break;
					} elseif (stripos($discount['type'], 'Voucher') !== false) { // Assuming 'Voucher' implies public
						$assessmentData['subsidy_status'] = 'PUB';
						break;
					}
					// Add check for 'PRV' if applicable
				}
			}

			$assessmentData['discount_amount'] = isset($summaryDetails['total_discount']) ? $summaryDetails['total_discount'] : 0.00;
			$assessmentData['payment_total'] =0.00;
			$assessmentData['outstanding_balance'] = $assessmentData['assessment_total']; // Initially, full payment is outstanding
			$assessmentData['module_balance'] = isset($summaryDetails['textbooks_supplies_total']) ? $summaryDetails['textbooks_supplies_total'] : 0.00;
			$assessmentData['rounding_off'] = 0.00000; // Per instruction
			$assessmentData['first'] = null; // Ignore
			$assessmentData['second'] = null; // Ignore
			$assessmentData['misc'] = null; // Ignore
			$assessmentData['status'] = 'PENDG'; // Per instruction

			// 2. Prepare AssessmentPaysched data
			$assessmentPayschedData = array();
			if (isset($summaryDetails['payment_schedules']) && is_array($summaryDetails['payment_schedules'])) {
				foreach ($summaryDetails['payment_schedules'] as $index => $schedule) {
					$dueDate = null;
					$billMonth = null;
					$transactionTypeId = 'SBQPY'; // Default for subsequent payments
					$dueAmount = isset($schedule['amount']) ? $schedule['amount'] : 0.00;
					// Handle "Upon Enrollment" specifically
					if (isset($schedule['schedule']) && $schedule['schedule'] == 'Upon Enrollment') {
						$dueDate = date('Y-m-d', strtotime('2025-06-15')); // Per instruction format YYYY-MM-DD
						$billMonth = 'UPONNROL';
						$transactionTypeId = 'INIPY';
					} else if (isset($schedule['schedule'])) {
						// Attempt to parse date and create bill month (e.g., "October 15, 2025")
						$parsedDate = strtotime($schedule['schedule']);
						if ($parsedDate) {
							$dueDate = date('Y-m-d', $parsedDate);
							$billMonth = strtoupper(date('MY', $parsedDate)); // e.g., OCT2025
						}
					}

					// Prepare AssessmentPaysched data
					$assessmentPayschedData[] = array(
						'assessment_id'=>$AID,
						'transaction_type_id' => $transactionTypeId,
						'bill_month' => $billMonth,
						'due_amount' =>$dueAmount,
						'paid_amount' => 0.00,
						'due_date' => $dueDate,
						'paid_date' => null,
						'status' => 'UNPD', // Assuming 'UNPD' for unpaid
						'order' => $index + 1,
					);
				}
			}

			// 3. Prepare AssessmentFee data
			$assessmentFeeData = array();
			$feeMap = array(
				'tuition_fee' => 'TUI',
				'miscellaneous_fees' => 'MSC',
				'other_fees' => 'OTH',
				'textbook_amount' => 'TXT',
				'supplies_amount' => 'SUP'
			);
			$order = 1;
			foreach ($feeMap as $summaryKey => $feeId) {
				if (isset($summaryDetails[$summaryKey]) && $summaryDetails[$summaryKey] > 0) {
					$assessmentFeeData[] = array(
						'assessment_id' => $AID,
						'fee_id' => $feeId,
						'due_amount' => $summaryDetails[$summaryKey],
						'paid_amount' => 0.00,
						'adjust_amount' => 0.00,
						'percentage' => null,
						'order' => $order++,
					);
				}
			}


			// 4. Prepare data for saveAll
			$saveData = array(
				'Assessment' => $assessmentData,
				'AssessmentPaysched' => $assessmentPayschedData,
				'AssessmentFee' => $assessmentFeeData
			);

			$this->Assessment->create();
			if ($this->Assessment->saveAll($saveData)) { // Use 'deep' for associated data
				$this->AssessmentRequest->updateStatus($incomingData['request_no'],'PROCESSED');
				if ($this->isAPIRequest()) {
					// Return success response, maybe the created assessment ID
					$this->set('assessment', array('Assessment' => array('id' => $this->Assessment->id, 'status' => 'created')));
					return;
				}
				$this->Session->setFlash(__('The assessment has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				// Log validation errors if helpful: $this->log($this->Assessment->validationErrors);
				if ($this->isAPIRequest()) {
					// Return error response
					return $this->cakeError('error500', array('message' => 'Could not save assessment', 'errors' => $this->Assessment->validationErrors));
				}
				$this->Session->setFlash(__('The assessment could not be saved. Please, try again.', true));
			}
		} elseif ($this->isAPIRequest()) {
            // Handle case where data is empty for API request
            return $this->cakeError('error400', array('message' => 'No assessment data provided.'));
        }

		// Load necessary data for dropdowns, etc. if rendering a form (non-API)
		if (!$this->isAPIRequest()) {
			$students = $this->Assessment->Student->find('list');
			$sections = $this->Assessment->Section->find('list');
			$this->set(compact('students', 'sections'));
		}
	}

	function edit($id = null) {
		if (!$id && empty($this->data)) {
			if ($this->isAPIRequest()) {
				return $this->cakeError('error400', array('message' => 'Invalid Assessment ID'));
			}
			$this->Session->setFlash(__('Invalid assessment ID', true));
			$this->redirect(array('action' => 'index'));
		}
		if (!empty($this->data)) {
			// Handle saving associated data updates carefully
			// May need to delete old associated records before saving new ones
			if ($this->Assessment->saveAll($this->data)) {
				if ($this->isAPIRequest()) {
					$this->set('assessment', array('Assessment' => array('id' => $id, 'status' => 'updated')));
					return;
				}
				$this->Session->setFlash(__('The assessment has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				if ($this->isAPIRequest()) {
					return $this->cakeError('error500', array('message' => 'Could not update assessment'));
				}
				$this->Session->setFlash(__('The assessment could not be saved. Please, try again.', true));
			}
		}
		if (empty($this->data)) {
			// Use containable to fetch data for the edit form
			$this->Assessment->contain(array(
				'Student',
				'Section',
				'AssessmentFee',
				'AssessmentPaysched',
				'AssessmentSubject'
			));
			$this->data = $this->Assessment->findById($id);
			if (!$this->data) {
				if ($this->isAPIRequest()) {
					return $this->cakeError('error404', array('message' => 'Assessment not found'));
				}
				$this->Session->setFlash(__('Assessment not found', true));
				$this->redirect(array('action' => 'index'));
			}
		}
		// Load necessary data for dropdowns, etc. in the edit form
		$students = $this->Assessment->Student->find('list');
		$sections = $this->Assessment->Section->find('list');
		$this->set(compact('students', 'sections'));
	}

	function delete($id = null) {
		if (!$id) {
			if ($this->isAPIRequest()) {
				return $this->cakeError('error400', array('message' => 'Invalid Assessment ID'));
			}
			$this->Session->setFlash(__('Invalid id for assessment', true));
			$this->redirect(array('action'=>'index'));
		}
		if ($this->Assessment->delete($id)) { // Assumes dependent=true in model associations handles related data
			if ($this->isAPIRequest()) {
				$this->set('assessment', array('Assessment' => array('id' => $id, 'status' => 'deleted')));
				return;
			}
			$this->Session->setFlash(__('Assessment deleted', true));
			$this->redirect(array('action'=>'index'));
		}
		if ($this->isAPIRequest()) {
			return $this->cakeError('error500', array('message' => 'Could not delete assessment'));
		}
		$this->Session->setFlash(__('Assessment was not deleted', true));
		$this->redirect(array('action' => 'index'));
	}

	function preview_email() {
		// Add StudentUpdate model to the uses array if not already included
		$this->uses[] = 'StudentUpdate';

		// Get assessment ID from URL parameters
		$assessmentId = isset($this->params['url']['id']) ? $this->params['url']['id'] : null;
		$emails = isset($this->params['url']['emails']) ? explode(',', $this->params['url']['emails']) : array();

		// Validate assessment ID
		if (!$assessmentId) {
			$this->set('error', 'Assessment ID is required');
			return;
		}

		// Get assessment data
		$this->Assessment->contain(array('Student'));
		$assessment = $this->Assessment->findById($assessmentId);

		if (!$assessment) {
			$this->set('error', 'Assessment not found');
			return;
		}

		$emailObj = $this->prepareEmailObj($assessment);
		// Set data for view
		$this->set('emailSubject', $emailObj['subject']);
		$this->set('emailBody', $emailObj['body']);
		$this->set('emails', $emails);
		$this->set('assessment', $assessment);

		// Set layout to display email preview
		$this->layout = 'ajax';

		// Render the view
		$this->render('preview_email');
	}
	function preview_attachment(){
		// Get assessment ID from URL parameters
		$assessmentId = isset($this->params['url']['id']) ? $this->params['url']['id'] : null;

		// Validate assessment ID
		if (!$assessmentId) {
			$this->set('error', 'Assessment ID is required');
			return;
		}

		// Get assessment data with related records
		$this->Assessment->contain(array(
			'Student',
			'AssessmentFee',
			'AssessmentPaysched'
		));
		$assessment = $this->Assessment->findById($assessmentId);

		if (!$assessment) {
			$this->set('error', 'Assessment not found');
			return;
		}

		// Load the AssessmentRequestForm class
		App::import('Vendor', 'AssessmentRequestForm', array('file' => 'reports/AssessmentRequestForm.php'));

		// Create the PDF form
		$pdf = new AssessmentRequestForm($assessment);

		// Output the PDF
		$filename = 'Assessment_' . $assessmentId . '.pdf';
		$pdf->Output($filename, 'I'); // 'I' means inline (display in browser)
		exit;
	}
	function send_email() {


		// Get data from POST request
		$data=  $this->data['Assessment'];
		$assessmentId = isset($data['assessment_id']) ? $data['assessment_id'] : null;
		$emails = isset($data['emails']) ? $data['emails'] : array();

		$response = array('success' => false, 'message' => '');

		// Validate assessment ID
		if (!$assessmentId) {
			$response['message'] = 'Assessment ID is required';
			$this->set('response', $response);
			return;
		}

		// Validate emails
		if (empty($emails)) {
			$response['message'] = 'No email recipients provided';
			$this->set('response', $response);
			return;
		}

		// Get assessment data with related records
		$this->Assessment->contain(array(
			'Student',
			'AssessmentFee',
			'AssessmentPaysched'
		));
		$assessment = $this->Assessment->findById($assessmentId);

		if (!$assessment) {
			$response['message'] = 'Assessment not found';
			$this->set('response', $response);
			return;
		}

		// Prepare email data
		$emailObj = $this->prepareEmailObj($assessment);

		// Generate PDF attachment
		$pdfAttachment = $this->generatePdfAttachment($assessment);
		if ($pdfAttachment) {
			$emailObj['attachment'] = $pdfAttachment;
		}

		// Send email to each recipient
		$sentCount = 0;
		foreach ($emails as $email) {
			$result = $this->StudentUpdate->logEmail($assessment['Assessment']['id'], $email, $emailObj);
			if ($result['status'] == 'SENT') {
				$sentCount++;
			}
		}

		// Clean up temporary file if it exists
		if (isset($pdfAttachment['path']) && file_exists($pdfAttachment['path'])) {
			@unlink($pdfAttachment['path']);
		}

		// Prepare response
		if ($sentCount > 0) {
			$response['success'] = true;
			$response['message'] = 'Email sent successfully to ' . $sentCount . ' recipient(s)';
		} else {
			$response['message'] = 'Failed to send email to any recipients';
		}

		$this->set('response', $response);
	}

	/**
	 * Generate PDF attachment for assessment
	 * @param array $assessment Assessment data
	 * @return array|false Attachment information or false on failure
	 */
	protected function generatePdfAttachment($assessment) {
		// Create temp directory if it doesn't exist
		$tempDir = TMP . 'attachments';
		if (!file_exists($tempDir)) {
			mkdir($tempDir, 0755, true);
		}

		// Generate a unique filename
		$filename = 'Assessment_' . $assessment['Assessment']['id'] . '.pdf';
		$tempPath = $tempDir . DS . $filename;

		try {
			// Load the AssessmentRequestForm class
			App::import('Vendor', 'AssessmentRequestForm', array('file' => 'reports/AssessmentRequestForm.php'));

			// Create the PDF form
			$pdf = new AssessmentRequestForm($assessment);

			// Save the PDF to the temp directory
			$pdf->Output($tempPath, 'F'); // 'F' means save to file

			// Check if file was created successfully
			if (file_exists($tempPath)) {
				return array(
					'name' => $filename,
					'path' => $tempPath,
					'type' => 'application/pdf'
				);
			}
		} catch (Exception $e) {
			// Log the error
			$this->log('Error generating PDF attachment: ' . $e->getMessage(), LOG_ERROR);
		}

		return false;
	}



	protected function prepareEmailObj($assessment) {
		// Prepare email data
		$appDetails =  json_decode($assessment['Assessment']['account_details'],true);

		if(isset($assessment['Student']['sno'])):
			$sno =trim($assessment['Student']['sno']);
			$student_name = str_replace($sno, '', $appDetails['student_name']);
		else:
			$student_name = str_replace('NEW','',$appDetails['student_name']);
		endif;
		$emailData = array(
			'ref_no' => $assessment['Assessment']['id'],
			'student_name' => $student_name,
			'year_level' => $appDetails['year_level'],
			'payment_plan' => $appDetails['payment_plan'],
			'total_tuition_misc_other' => $appDetails['total_tuition_misc_other'],
			'reservation_fee_amount' => $appDetails['reservation_fee_amount'],
			'total_discount' => $appDetails['total_discount'],
			'textbooks_supplies_total' => $appDetails['textbooks_supplies_total'],
			'assessment_total' => $assessment['Assessment']['assessment_total']
		);

		$amountFields = ['total_tuition_misc_other', 'reservation_fee_amount', 'total_discount', 'textbooks_supplies_total', 'assessment_total'];

		foreach($emailData as $key => $value) {
			$numVal = (float)$value;
			$isFormat = in_array($key,$amountFields);
			if($isFormat) {
				$value = $numVal?number_format($value,2,'.',','):' — &nbsp;&nbsp;';
				if($numVal):
					if($key=='total_discount' || $key =='reservation_fee_amount'):
						$value = "($value)";
					endif;
				endif;
				$emailData[$key] = $value;
			}

		}

		// Generate email preview using StudentUpdate model
		$emailObj = $this->StudentUpdate->prepareEmail('assessment_processed', $emailData);
		return $emailObj;
	}
}
?>
