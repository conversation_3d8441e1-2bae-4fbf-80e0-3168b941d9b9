<?php
class Assessment extends AppModel {
	var $name = 'Assessment';
	var $useTable = 'assessments'; // Explicitly define table name
	var $displayField = 'ref_no'; // Or another suitable field
	var $actsAs = array('Containable');

	var $belongsTo = array(
		'Student' => array(
			'className' => 'Student',
			'foreignKey' => 'student_id',
			'conditions' => '',
			'fields' => '',
			'order' => ''
		),
		'Section' => array(
			'className' => 'Section',
			'foreignKey' => 'section_id',
			'conditions' => '',
			'fields' => '',
			'order' => ''
		),
		'AssessmentRequest' => array(
			'className' => 'AssessmentRequest',
			'foreignKey' => 'ref_no',
			'conditions' => '',
			'fields' => '',
			'order' => ''
		)	
		// Add other belongsTo associations if needed based on foreign keys like account_type?
	);

	var $hasMany = array(
		'AssessmentFee' => array(
			'className' => 'AssessmentFee',
			'foreignKey' => 'assessment_id',
			'dependent' => true, // Set to true if fees should be deleted when assessment is deleted
			'conditions' => '',
			'fields' => '',
			'order' => 'AssessmentFee.order', // Example order
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		),
		'AssessmentPaysched' => array(
			'className' => 'AssessmentPaysched',
			'foreignKey' => 'assessment_id',
			'dependent' => true,
			'conditions' => '',
			'fields' => '',
			'order' => 'AssessmentPaysched.order', // Example order
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		)
	);

	// Add custom model methods if needed, similar to generateAID or getEnrolled from the example
	function generateAID($prefix='CJA'){ // Example based on sem/assessment.php
		$ID = 0;
		$cond =  array('Assessment.id LIKE'=>$prefix.'%');
		$this->recursive=-1;
		$lastAssessment = $this->find('first',array('conditions'=>$cond,'order'=>array('Assessment.id'=>'desc'), 'fields'=>array('Assessment.id')));

		if($lastAssessment)
			$ID =  (int)(str_replace($prefix, '', $lastAssessment['Assessment']['id']));

		// Ensure the generated ID fits within the char(8) limit defined in the schema
		$newIdNumber = $ID + 1;
		$paddingLength = 10 - strlen($prefix);
		if ($paddingLength < 0) $paddingLength = 0; // Avoid negative padding

		$newId = $prefix . str_pad($newIdNumber, $paddingLength, '0', STR_PAD_LEFT);

		// Check if the generated ID exceeds the length limit
		if (strlen($newId) > 10) {
			// Handle error: ID length exceeded. Maybe throw an exception or log an error.
			// For now, let's return a truncated ID or handle as appropriate for the application.
			// This part needs careful consideration based on application requirements.
			trigger_error("Generated Assessment ID exceeds maximum length.", E_USER_WARNING);
			return substr($newId, 0, 10); // Example: truncate, might not be ideal
		}

		return $newId;
	}

	function getDetails($AID){
		$conf = array(
			'recursive'=>-1,
			'conditions'=>array('Assessment.id'=>$AID),
			'fields'=>array('id','student_id','section_id','esp','ref_no','payment_scheme','assessment_total','discount_amount','outstanding_balance','status','account_details')
		);
		return $this->find('first',$conf);
	}
}
?>
