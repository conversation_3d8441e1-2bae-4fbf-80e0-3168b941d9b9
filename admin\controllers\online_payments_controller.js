"use strict";
define(['app','atomic/bomb','api'], function (app) {
    app.register.controller('OnlinePaymentsController',['$scope','api',function ($scope,api) {
        const $selfScope = $scope;
        $scope = this;
        $scope.init = function () {
            alert(1);
            // Initialize the controller
            $scope.payments = [];
           $scope.headers = ['Date Submitted','Ref No.','Student Name','Incoming Grade Level','Payment Type','Amount Paid','Status'];
            $scope.props = ['transaction_date','ref_no','student_name','incoming_level','payment_method','amount','payment_status'];
             $scope.isLoading = false;
            $scope.loadPayments();
        };

        $scope.loadPayments = function () {
            $scope.isLoading = true;
            api.GET('online_payments', {}, function (response) {
                $scope.payments = response.data;
                $scope.isLoading = false;
            }, function (error) {
                console.error('Error loading payments:', error);
                $scope.isLoading = false;
            });
        };

        $scope.init();
    }]);
});
